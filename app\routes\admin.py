from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from app import db
from app.models.hotel import Hotel
from app.models.department import Department
from app.models.learner import Learner
from app.models.company_culture import CompanyCulture
from app.models.question import Question

bp = Blueprint('admin', __name__)


# 辅助函数：检查是否为管理员（实际项目中应实现更完善的权限控制）
def is_admin():
    """简单的管理员判断，实际项目应设计专门的权限系统"""
    # 这里假设员工工号为"admin"的是管理员
    return current_user.is_authenticated and current_user.employee_id == 'admin'

@bp.route('/login', methods=['GET', 'POST'])
def login_page():
    """管理员登录页面和登录处理"""
    if request.method == 'POST':
        from flask_login import login_user
        from werkzeug.security import check_password_hash
        from app.models.learner import Learner

        employee_id = request.form.get('employee_id')
        password = request.form.get('password')

        # 查找管理员账号
        admin_user = Learner.query.filter_by(employee_id=employee_id, status=1).first()

        if not admin_user:
            flash('管理员账号不存在', 'danger')
            return redirect(url_for('admin.login_page'))

        # 验证密码
        if not check_password_hash(admin_user.reserved1 or '', password):
            flash('密码错误', 'danger')
            return redirect(url_for('admin.login_page'))

        # 验证是否为管理员
        if admin_user.employee_id != 'admin':
            flash('没有管理员权限', 'danger')
            return redirect(url_for('admin.login_page'))

        # 登录管理员
        login_user(admin_user)
        return redirect(url_for('admin.dashboard'))

    return render_template('admin/login.html')

@bp.before_request
def check_admin_permission():
    """路由访问前检查管理员权限"""
    # 登录页面不需要权限检查
    if request.endpoint == 'admin.login_page':
        return

    if not is_admin():
        flash('请先登录管理员账号', 'danger')
        return redirect(url_for('admin.login_page'))


@bp.route('/dashboard')
def dashboard():
    """管理员仪表盘"""
    hotel_count = Hotel.query.count()
    dept_count = Department.query.count()
    learner_count = Learner.query.count()
    culture_count = CompanyCulture.query.count()
    question_count = Question.query.count()

    return render_template('admin/dashboard.html',
                           hotel_count=hotel_count,
                           dept_count=dept_count,
                           learner_count=learner_count,
                           culture_count=culture_count,
                           question_count=question_count)


# 酒店管理
@bp.route('/hotels')
def list_hotels():
    """酒店列表"""
    hotels = Hotel.query.all()
    return render_template('admin/hotels/list.html', hotels=hotels)


@bp.route('/hotels/add', methods=['GET', 'POST'])
def add_hotel():
    """添加酒店"""
    if request.method == 'POST':
        name = request.form.get('name')
        address = request.form.get('address')
        phone = request.form.get('phone')

        if Hotel.query.filter_by(name=name).first():
            flash('酒店名称已存在', 'danger')
            return redirect(url_for('admin.add_hotel'))

        hotel = Hotel(
            name=name,
            address=address,
            phone=phone
        )
        db.session.add(hotel)
        db.session.commit()

        flash('酒店添加成功', 'success')
        return redirect(url_for('admin.list_hotels'))

    return render_template('admin/hotels/add.html')


@bp.route('/hotels/edit/<int:hotel_id>', methods=['GET', 'POST'])
def edit_hotel(hotel_id):
    """编辑酒店"""
    hotel = Hotel.query.get_or_404(hotel_id)

    if request.method == 'POST':
        # 检查名称是否已被其他酒店使用
        name = request.form.get('name')
        if name != hotel.name and Hotel.query.filter_by(name=name).first():
            flash('酒店名称已存在', 'danger')
            return redirect(url_for('admin.edit_hotel', hotel_id=hotel_id))

        hotel.name = name
        hotel.address = request.form.get('address')
        hotel.phone = request.form.get('phone')
        db.session.commit()

        flash('酒店信息已更新', 'success')
        return redirect(url_for('admin.list_hotels'))

    return render_template('admin/hotels/edit.html', hotel=hotel)


# 部门管理
@bp.route('/departments')
def list_departments():
    """部门列表"""
    departments = Department.query.join(Hotel).add_columns(
        Hotel.name.label('hotel_name')
    ).all()
    return render_template('admin/departments/list.html', departments=departments)


@bp.route('/departments/add', methods=['GET', 'POST'])
def add_department():
    """添加部门"""
    hotels = Hotel.query.all()

    if request.method == 'POST':
        hotel_id = request.form.get('hotel_id')
        name = request.form.get('name')
        description = request.form.get('description')

        # 检查同一酒店内部门名称是否重复
        if Department.query.filter_by(hotel_id=hotel_id, name=name).first():
            flash('该酒店下已存在同名部门', 'danger')
            return redirect(url_for('admin.add_department'))

        department = Department(
            hotel_id=hotel_id,
            name=name,
            description=description
        )
        db.session.add(department)
        db.session.commit()

        flash('部门添加成功', 'success')
        return redirect(url_for('admin.list_departments'))

    return render_template('admin/departments/add.html', hotels=hotels)


# 员工管理
@bp.route('/learners')
def list_learners():
    """员工列表"""
    learners = Learner.query.join(Department).join(Hotel).add_columns(
        Department.name.label('dept_name'),
        Hotel.name.label('hotel_name')
    ).all()
    return render_template('admin/learners/list.html', learners=learners)


@bp.route('/learners/add', methods=['GET', 'POST'])
def add_learner():
    """添加员工"""
    departments = Department.query.all()

    if request.method == 'POST':
        department_id = request.form.get('department_id')
        name = request.form.get('name')
        employee_id = request.form.get('employee_id')
        phone = request.form.get('phone')
        email = request.form.get('email')

        # 检查员工工号是否重复
        if Learner.query.filter_by(employee_id=employee_id).first():
            flash('员工工号已存在', 'danger')
            return redirect(url_for('admin.add_learner'))

        # 创建员工（密码默认设置为123456，实际项目中应使用哈希）
        from werkzeug.security import generate_password_hash
        learner = Learner(
            department_id=department_id,
            name=name,
            employee_id=employee_id,
            phone=phone,
            email=email,
            reserved1=generate_password_hash('123456')  # 临时存储密码哈希
        )
        db.session.add(learner)
        db.session.commit()

        flash('员工添加成功，初始密码为123456', 'success')
        return redirect(url_for('admin.list_learners'))

    return render_template('admin/learners/add.html', departments=departments)


# 企业文化管理
@bp.route('/cultures')
def list_cultures():
    """企业文化列表"""
    cultures = CompanyCulture.query.join(Hotel).add_columns(
        Hotel.name.label('hotel_name')
    ).all()
    return render_template('admin/cultures/list.html', cultures=cultures)


@bp.route('/cultures/add', methods=['GET', 'POST'])
def add_culture():
    """添加企业文化"""
    hotels = Hotel.query.all()

    if request.method == 'POST':
        hotel_id = request.form.get('hotel_id')
        title = request.form.get('title')
        content = request.form.get('content')

        culture = CompanyCulture(
            hotel_id=hotel_id,
            title=title,
            content=content
        )
        db.session.add(culture)
        db.session.commit()

        flash('企业文化添加成功', 'success')
        return redirect(url_for('admin.list_cultures'))

    return render_template('admin/cultures/add.html', hotels=hotels)


# 题库管理
@bp.route('/questions')
def list_questions():
    """试题列表"""
    questions = Question.query.join(CompanyCulture).add_columns(
        CompanyCulture.title.label('culture_title')
    ).all()
    return render_template('admin/questions/list.html', questions=questions)


@bp.route('/questions/add', methods=['GET', 'POST'])
def add_question():
    """添加试题"""
    cultures = CompanyCulture.query.all()

    if request.method == 'POST':
        culture_id = request.form.get('culture_id')
        content = request.form.get('content')
        question_type = int(request.form.get('question_type'))
        score = int(request.form.get('score', 10))
        explanation = request.form.get('explanation')

        # 处理选项（单选/多选）
        options = None
        if question_type in [1, 2]:
            option_labels = ['A', 'B', 'C', 'D', 'E', 'F']
            options_list = []
            for i, label in enumerate(option_labels):
                option_content = request.form.get(f'option_{label}')
                if option_content:
                    options_list.append({
                        'id': label,
                        'content': option_content
                    })
            if not options_list:
                flash('请至少添加一个选项', 'danger')
                return redirect(url_for('admin.add_question'))
            options = str(options_list).replace("'", '"')  # 转换为JSON格式字符串

        # 处理正确答案
        correct_answer = request.form.get('correct_answer')
        if not correct_answer:
            flash('请填写正确答案', 'danger')
            return redirect(url_for('admin.add_question'))

        question = Question(
            culture_id=culture_id,
            content=content,
            question_type=question_type,
            options=options,
            correct_answer=correct_answer,
            score=score,
            explanation=explanation
        )
        db.session.add(question)
        db.session.commit()

        flash('试题添加成功', 'success')
        return redirect(url_for('admin.list_questions'))

    return render_template('admin/questions/add.html', cultures=cultures)

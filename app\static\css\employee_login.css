/* 全局样式重置，简化默认样式差异 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Microsoft YaHei", sans-serif; 
  background-color: #e9f2ff; 
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh; 
}

/* 登录容器样式 */
.login-container {
  background-color: #fff; 
  width: 380px; 
  padding: 30px 25px; 
  border-radius: 8px; 
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); 
}

/* 登录标题样式 */
.login-title {
  font-size: 24px; 
  color: #4040c4; 
  text-align: center; 
  margin-bottom: 8px; 
}

/* 登录描述文本样式 */
.login-desc {
  font-size: 14px; 
  color: #999; 
  text-align: center; 
  margin-bottom: 20px; 
}

/* 表单组样式，包含标签和输入/选择框 */
.form-group {
  margin-bottom: 18px; 
}

.form-group label {
  display: block; 
  font-size: 14px; 
  color: #333; 
  margin-bottom: 6px; 
}

.form-group select,
.form-group input {
  width: 100%; 
  padding: 10px; 
  font-size: 14px; 
  border: 1px solid #ccc; 
  border-radius: 4px; 
  outline: none; 
}

.form-group select:focus,
.form-group input:focus {
  border-color: #999; 
}

/* 登录按钮样式 */
.login-btn {
  width: 100%; 
  padding: 10px; 
  font-size: 16px; 
  color: #fff; 
  background-color: #5757e0; 
  border: none; 
  border-radius: 4px; 
  cursor: pointer; 
  transition: background-color 0.3s ease; 
}

.login-btn:hover {
  background-color: #4040c4; 
}

/* 表单底部提示文本样式 */
.form-note {
  text-align: center; 
  font-size: 13px; 
  color: #999; 
  margin-top: 15px; 
  line-height: 1.6; 
}
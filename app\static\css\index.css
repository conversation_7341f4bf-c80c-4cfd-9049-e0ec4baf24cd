body {
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
    background-color: #f0f5ff;
    color: #333;
    display: flex;
    flex-direction: column;
    align-items: center; /* 让body下的内容居中，配合容器宽度实现对称留白 */
}

/* 头部导航样式 */
.header {
    width: 1000px; /* 设置头部导航固定宽度 */
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0; /* 调整内边距，左右留白由固定宽度和居中实现 */
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.header-left {
    display: flex;
    align-items: center;
    padding-left: 10px;
}

.header-left i {
    font-size: 24px;
    margin-right: 10px;
    color: #007bff;
}

.header-title {
    font-size: 20px;
    font-weight: bold;
}

.header-right {
    padding-right: 10px;
}

.header-right .btn {
    padding: 8px 15px;
    margin-left: 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.employee-login {
    background-color: #fff;
    color: #333;
    border: 1px solid #ddd;
}

.admin-login {
    background-color: #000;
    color: #fff;
}

/* 欢迎横幅样式 */
.banner {
    width: 900px; /* 设置横幅固定宽度 */
    background: linear-gradient(90deg, #42a5f5, #7e57c2);
    color: #fff;
    text-align: center;
    padding: 40px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.banner h1 {
    font-size: 28px;
    margin-bottom: 10px;
}

.banner p {
    font-size: 16px;
}

/* 功能模块样式 */
.features {
    width: 1000px; /* 设置功能模块容器固定宽度 */
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between; /* 调整为两端对齐，让卡片分布更合理 */
    margin-bottom: 20px;
}

.feature-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    width: calc(50% - 10px); /* 调整宽度，留出卡片间的间距 */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
}

.feature-icon {
    font-size: 32px;
    margin-bottom: 10px;
}

.blue {
    color: #42a5f5;
}

.green {
    color: #4caf50;
}

.purple {
    color: #9c27b0;
}

.orange {
    color: #ff9800;
}

.feature-card h3 {
    font-size: 20px;
    margin-bottom: 10px;
}

.feature-card p {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
}

.feature-card .btn {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.start-learn, .start-exam {
    background-color: #000;
    color: #fff;
}

.employee-entrance, .admin-entrance {
    background-color: #fff;
    color: #333;
    border: 1px solid #ddd;
}

/* 系统特色样式 */
.system-features {
    width: 1000px; /* 设置系统特色模块固定宽度 */
    margin-bottom: 20px;
}

.system-features h2 {
    font-size: 22px;
    margin-bottom: 15px;
}

.system-features ul {
    list-style: none;
    padding: 0;
}

.system-features li {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.system-features li i {
    font-size: 18px;
    margin-right: 10px;
}

/* 培训内容样式 */
.training-content {
    width: 1000px; /* 设置培训内容模块固定宽度 */
    margin-bottom: 30px;
}

.training-content h2 {
    font-size: 22px;
    margin-bottom: 15px;
}

.content-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
}

.content-card h3 {
    font-size: 18px;
    margin-bottom: 5px;
}

.content-card p {
    font-size: 14px;
    color: #666;
}

/* 页脚样式 */
.footer {
    width: 100%; /* 页脚宽度占满，背景色铺满 */
    background-color: #2c3e50;
    color: #fff;
    text-align: center;
    padding: 20px;
}

.footer p {
    margin: 5px 0;
    font-size: 14px;
}
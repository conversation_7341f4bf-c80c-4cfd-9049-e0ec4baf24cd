body {
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
    background-color: #e6f0ff;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

.login-container {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    width: 350px;
    text-align: center;
}

.login-title {
    color: #333;
    margin-bottom: 10px;
    font-size: 24px;
}

.login-subtitle {
    color: #666;
    margin-bottom: 20px;
    font-size: 14px;
}

.form-group {
    margin-bottom: 15px;
    text-align: left;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #333;
    font-size: 14px;
}

.form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 14px;
}

.login-btn {
    width: 100%;
    padding: 12px;
    background-color: #4040c0;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    margin-bottom: 20px;
}

.login-btn:hover {
    background-color: #3030a0;
}

.login-info {
    color: #999;
    font-size: 12px;
    line-height: 1.5;
}

/* 消息提示样式 */
.alert {
    padding: 10px;
    margin-bottom: 15px;
    border-radius: 4px;
    font-size: 14px;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.login-info a {
    color: #4040c0;
    text-decoration: none;
}

.login-info a:hover {
    text-decoration: underline;
}
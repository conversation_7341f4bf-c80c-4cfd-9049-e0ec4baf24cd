<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 酒店培训系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/login.css') }}">
</head>
<body>
    <div class="login-container">
        <h2 class="login-title">管理员登录</h2>
        <p class="login-subtitle">酒店培训系统管理后台</p>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form method="POST" action="{{ url_for('admin.login_page') }}">
            <div class="form-group">
                <label for="employee_id">管理员账号:</label>
                <input type="text" id="employee_id" name="employee_id" required placeholder="请输入管理员账号">
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" required placeholder="请输入密码">
            </div>
            
            <button type="submit" class="login-btn">登录</button>
        </form>
        
        <div class="login-info">
            <p>管理员账号：admin</p>
            <p>默认密码：123456</p>
            <p><a href="{{ url_for('index') }}">返回首页</a></p>
        </div>
    </div>
</body>
</html>

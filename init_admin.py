#!/usr/bin/env python3
"""
初始化管理员账号脚本
运行此脚本来创建管理员账号和基础数据
"""

from app import create_app, db
from app.models.hotel import Hotel
from app.models.department import Department
from app.models.learner import Learner
from werkzeug.security import generate_password_hash

def init_admin():
    """初始化管理员账号和基础数据"""
    app = create_app()
    
    with app.app_context():
        # 创建数据库表
        db.create_all()
        
        # 检查是否已存在管理员
        admin_user = Learner.query.filter_by(employee_id='admin').first()
        if admin_user:
            print("管理员账号已存在")
            return
        
        # 创建默认酒店
        hotel = Hotel.query.filter_by(name='默认酒店').first()
        if not hotel:
            hotel = Hotel(
                name='默认酒店',
                address='默认地址',
                phone='000-0000-0000'
            )
            db.session.add(hotel)
            db.session.commit()
            print("创建默认酒店")
        
        # 创建管理部门
        admin_dept = Department.query.filter_by(name='管理部门').first()
        if not admin_dept:
            admin_dept = Department(
                hotel_id=hotel.id,
                name='管理部门',
                description='系统管理部门'
            )
            db.session.add(admin_dept)
            db.session.commit()
            print("创建管理部门")
        
        # 创建管理员账号
        admin_user = Learner(
            department_id=admin_dept.id,
            name='系统管理员',
            employee_id='admin',
            phone='000-0000-0000',
            email='<EMAIL>',
            status=1,
            reserved1=generate_password_hash('123456')  # 密码哈希
        )
        db.session.add(admin_user)
        db.session.commit()
        
        print("管理员账号创建成功！")
        print("账号：admin")
        print("密码：123456")
        print("请及时修改默认密码")

if __name__ == '__main__':
    init_admin()
